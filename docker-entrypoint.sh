#!/bin/bash
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 等待 PostgreSQL 启动
wait_for_postgres() {
    log_info "等待 PostgreSQL 启动..."
    
    # 启动 PostgreSQL 服务
    service postgresql start
    
    # 等待 PostgreSQL 完全启动
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if su - postgres -c "pg_isready -h localhost -p 5432" > /dev/null 2>&1; then
            log_info "PostgreSQL 已启动并准备就绪"
            return 0
        fi
        
        log_debug "等待 PostgreSQL 启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "PostgreSQL 启动超时"
    return 1
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 检查数据库是否已存在
    if su - postgres -c "psql -lqt | cut -d \| -f 1 | grep -qw fastapi_db"; then
        log_info "数据库 fastapi_db 已存在，跳过创建"
    else
        log_info "创建数据库和用户..."
        su - postgres -c "psql --command \"CREATE USER fastapi_user WITH SUPERUSER PASSWORD 'fastapi_password';\""
        su - postgres -c "createdb -O fastapi_user fastapi_db"
        log_info "数据库创建完成"
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    cd /app
    
    # 设置环境变量
    export DATABASE_URL="postgresql://fastapi_user:fastapi_password@localhost:5432/fastapi_db"
    export ENVIRONMENT="production"
    export DEBUG="false"
    
    # 运行 Python 数据库初始化脚本
    if python3 -c "
import sys
sys.path.insert(0, '/app')
from app.database import init_db, check_db_connection
import asyncio

async def main():
    if await check_db_connection():
        await init_db()
        print('数据库初始化完成')
        return True
    else:
        print('数据库连接失败')
        return False

result = asyncio.run(main())
sys.exit(0 if result else 1)
"; then
        log_info "数据库迁移完成"
    else
        log_warn "数据库迁移失败，但继续启动应用"
    fi
}

# 设置权限
setup_permissions() {
    log_info "设置文件权限..."
    
    # 确保日志目录存在并有正确权限
    mkdir -p /app/logs
    chmod 755 /app/logs
    
    # 确保上传目录存在并有正确权限
    mkdir -p /app/uploads /app/uploads/avatars
    chmod 755 /app/uploads /app/uploads/avatars
    
    # 设置应用文件权限
    chown -R root:root /app
    chmod +x /app/run.py
}

# 主函数
main() {
    log_info "=== FastAPI + PostgreSQL 容器启动 ==="
    log_info "容器启动时间: $(date)"
    
    # 设置权限
    setup_permissions
    
    # 等待并启动 PostgreSQL
    if ! wait_for_postgres; then
        log_error "PostgreSQL 启动失败，退出"
        exit 1
    fi
    
    # 初始化数据库
    init_database
    
    # 运行数据库迁移
    run_migrations
    
    log_info "=== 启动完成，开始运行服务 ==="
    
    # 执行传入的命令
    exec "$@"
}

# 如果脚本被直接执行，运行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
