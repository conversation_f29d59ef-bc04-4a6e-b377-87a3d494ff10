# 优化的 Dockerfile - 单容器方案
# 包含 FastAPI 后端和 PostgreSQL 数据库，使用现有数据库备份

FROM python:3.11-slim

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV TZ=Asia/Shanghai

# 配置国内镜像源以解决网络问题
RUN echo "deb https://mirrors.aliyun.com/debian/ bullseye main non-free contrib" > /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian/ bullseye main non-free contrib" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security/ bullseye-security main" >> /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian-security/ bullseye-security main" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ bullseye-updates main non-free contrib" >> /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian/ bullseye-updates main non-free contrib" >> /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    postgresql \
    postgresql-contrib \
    postgresql-client \
    libpq-dev \
    build-essential \
    curl \
    tzdata \
    supervisor \
    procps \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/"$TZ" /etc/localtime && echo "$TZ" > /etc/timezone

# 创建应用目录
WORKDIR /app

# 复制 requirements.txt 并安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码和数据库备份文件
COPY . .

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploads /app/uploads/avatars

# 配置 PostgreSQL
USER postgres

# 初始化数据库集群
RUN /usr/lib/postgresql/*/bin/initdb -D /var/lib/postgresql/data

# 修改 PostgreSQL 配置以允许本地连接
RUN echo "local all all trust" > /var/lib/postgresql/data/pg_hba.conf && \
    echo "host all all 127.0.0.1/32 md5" >> /var/lib/postgresql/data/pg_hba.conf && \
    echo "host all all ::1/128 md5" >> /var/lib/postgresql/data/pg_hba.conf && \
    echo "listen_addresses='localhost'" >> /var/lib/postgresql/data/postgresql.conf && \
    echo "port=5432" >> /var/lib/postgresql/data/postgresql.conf

# 启动数据库并导入数据
RUN /usr/lib/postgresql/*/bin/pg_ctl -D /var/lib/postgresql/data start && \
    sleep 5 && \
    /usr/lib/postgresql/*/bin/psql --command "CREATE USER fastapi_user WITH SUPERUSER PASSWORD 'fastapi_password';" && \
    /usr/lib/postgresql/*/bin/createdb -O fastapi_user fastapi_db && \
    /usr/lib/postgresql/*/bin/psql -U fastapi_user -d fastapi_db -f /app/aiqcj_2025-07-29_11-45-32_pgsql_data.sql && \
    /usr/lib/postgresql/*/bin/pg_ctl -D /var/lib/postgresql/data stop

USER root

# 创建 supervisor 配置和设置启动脚本
RUN mkdir -p /var/log/supervisor
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# 创建 supervisor 配置文件
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 设置启动命令
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
