#!/bin/bash
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 等待 PostgreSQL 启动
wait_for_postgres() {
    log_info "等待 PostgreSQL 启动..."

    # 停止系统默认的 PostgreSQL 服务
    service postgresql stop > /dev/null 2>&1 || true

    # 启动我们自定义的 PostgreSQL 实例
    su - postgres -c "/usr/lib/postgresql/*/bin/pg_ctl -D /var/lib/postgresql/data start" > /dev/null 2>&1

    # 等待 PostgreSQL 完全启动
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if su - postgres -c "/usr/lib/postgresql/*/bin/pg_isready -h localhost -p 5432" > /dev/null 2>&1; then
            log_info "PostgreSQL 已启动并准备就绪"
            return 0
        fi

        log_debug "等待 PostgreSQL 启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done

    log_error "PostgreSQL 启动超时"
    return 1
}

# 初始化数据库
init_database() {
    log_info "检查数据库状态..."

    # 检查数据库是否已存在且有数据
    if su - postgres -c "psql -lqt | cut -d \| -f 1 | grep -qw fastapi_db"; then
        log_info "数据库 fastapi_db 已存在"

        # 检查是否有数据表
        table_count=$(su - postgres -c "/usr/lib/postgresql/*/bin/psql -d fastapi_db -t -c \"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';\"" 2>/dev/null | tr -d ' ' | head -1)

        if [ "$table_count" -gt 0 ] 2>/dev/null; then
            log_info "数据库已包含 $table_count 个表，跳过初始化"
            return 0
        else
            log_info "数据库为空，需要导入数据"
        fi
    else
        log_info "创建数据库和用户..."
        su - postgres -c "psql --command \"CREATE USER fastapi_user WITH SUPERUSER PASSWORD 'fastapi_password';\"" || true
        su - postgres -c "createdb -O fastapi_user fastapi_db"
        log_info "数据库创建完成"
    fi

    # 导入数据库备份
    if [ -f "/app/aiqcj_2025-07-29_11-45-32_pgsql_data.sql" ]; then
        log_info "导入数据库备份文件..."
        if su - postgres -c "/usr/lib/postgresql/*/bin/psql -d fastapi_db -f /app/aiqcj_2025-07-29_11-45-32_pgsql_data.sql" 2>/dev/null; then
            log_info "数据库备份导入成功"
        else
            log_warn "数据库备份导入失败，但继续启动（可能数据已存在）"
        fi
    else
        log_warn "未找到数据库备份文件，跳过导入"
    fi
}

# 验证数据库连接
verify_database() {
    log_info "验证数据库连接..."

    cd /app

    # 设置环境变量
    export DATABASE_URL="postgresql://fastapi_user:fastapi_password@localhost:5432/fastapi_db"
    export ENVIRONMENT="production"
    export DEBUG="false"

    # 验证数据库连接
    if python -c "
import sys
sys.path.insert(0, '/app')
from app.database import check_db_connection
import asyncio

async def main():
    if await check_db_connection():
        print('数据库连接正常')
        return True
    else:
        print('数据库连接失败')
        return False

result = asyncio.run(main())
sys.exit(0 if result else 1)
"; then
        log_info "数据库连接验证成功"
        return 0
    else
        log_error "数据库连接验证失败"
        return 1
    fi
}

# 设置权限
setup_permissions() {
    log_info "设置文件权限..."
    
    # 确保日志目录存在并有正确权限
    mkdir -p /app/logs
    chmod 755 /app/logs
    
    # 确保上传目录存在并有正确权限
    mkdir -p /app/uploads /app/uploads/avatars
    chmod 755 /app/uploads /app/uploads/avatars
    
    # 设置应用文件权限
    chown -R root:root /app
    chmod +x /app/run.py
}

# 主函数
main() {
    log_info "=== FastAPI + PostgreSQL 容器启动 ==="
    log_info "容器启动时间: $(date)"
    
    # 设置权限
    setup_permissions
    
    # 等待并启动 PostgreSQL
    if ! wait_for_postgres; then
        log_error "PostgreSQL 启动失败，退出"
        exit 1
    fi
    
    # 数据库已在构建时初始化，跳过运行时初始化
    log_info "数据库已在构建时初始化，跳过运行时初始化"

    # 验证数据库连接
    if ! verify_database; then
        log_warn "数据库验证失败，但继续启动应用"
    fi
    
    log_info "=== 启动完成，开始运行服务 ==="
    
    # 执行传入的命令
    exec "$@"
}

# 如果脚本被直接执行，运行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
